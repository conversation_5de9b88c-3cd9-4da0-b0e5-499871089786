import { initializeApp, getApp, getApps } from "firebase/app";
import { getAuth, connectAuthEmulator, GoogleAuthProvider } from "firebase/auth";

// Firebase App initialization using environment variables.
// In development, we connect Auth to the local emulator.
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "fake-api-key",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "localhost",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "demo-planfuly",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "demo-app-id",
};

const app = getApps().length ? getApp() : initializeApp(firebaseConfig);
const auth = getAuth(app);

if (import.meta.env.DEV) {
  try {
    // Avoid duplicate emulator connection in HMR
    // @ts-ignore – mark once connected
    if (!(auth as any)._emulatorConnected) {
      const emulatorUrl = import.meta.env.VITE_FIREBASE_AUTH_EMULATOR_URL || "http://127.0.0.1:9099";
      connectAuthEmulator(auth, emulatorUrl, { disableWarnings: true });
      // @ts-ignore
      (auth as any)._emulatorConnected = true;
    }
  } catch (err) {
    // no-op; emulator connection is best-effort in dev
    console.warn("Auth emulator connection warning:", err);
  }
}

const googleProvider = new GoogleAuthProvider();

export { app, auth, googleProvider };

