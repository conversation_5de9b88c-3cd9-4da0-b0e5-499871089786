import { Layout } from "@/components/ui/layout"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/container/card"
import { <PERSON><PERSON> } from "@/components/ui/basic/button"
import { Plus, Mail, Phone } from "lucide-react"
import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"


type Client = {
  id: string
  name: string
  status: "Active" | "Prospect" | "Inactive"
  contactEmail?: string
  contactPhone?: string
}

function ClientList() {
  const [clients, setClients] = useState<Client[] | null>(null)
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    // TODO: Replace with real data fetch
    const timer = setTimeout(() => {
      setClients([
        { id: "1", name: "Acme Corp", status: "Active", contactEmail: "<EMAIL>", contactPhone: "(*************" },
        { id: "2", name: "Globex LLC", status: "Prospect", contactEmail: "<EMAIL>" },
      ])
      setLoading(false)
    }, 400)
    return () => clearTimeout(timer)
  }, [])

  const goToAddClient = () => navigate("/clients/new")

  return (
    <Layout title="Clients">
      <section className="max-w-7xl mx-auto px-4 py-6">

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 rounded-lg bg-muted/50 animate-pulse" />
            ))}
          </div>
        ) : (clients && clients.length > 0) ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {clients.map((c) => (
              <Card key={c.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">{c.name}</CardTitle>
                  <CardDescription>Status: {c.status}</CardDescription>
                </CardHeader>
                <CardContent className="pt-0 text-sm space-y-1">
                  {c.contactEmail && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Mail className="size-4" />
                      <span>{c.contactEmail}</span>
                    </div>
                  )}
                  {c.contactPhone && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Phone className="size-4" />
                      <span>{c.contactPhone}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}

            <Card
              className="flex items-center justify-center border-dashed cursor-pointer hover:bg-accent/40 transition-colors"
              onClick={goToAddClient}
              role="button"
              aria-label="Add client"
            >
              <div className="flex flex-col items-center gap-2 p-8">
                <Button variant="outline" size="lg" className="gap-2" onClick={goToAddClient}>
                  <Plus />
                  Add client
                </Button>
                <p className="text-xs text-muted-foreground">Create a new client</p>
              </div>
            </Card>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center rounded-lg border border-dashed py-14">
            <p className="text-sm text-muted-foreground mb-4">No clients yet.</p>
            <Button onClick={goToAddClient} className="gap-2">
              <Plus />
              Add your first client
            </Button>
          </div>
        )}
      </section>
    </Layout>
  )
}

export default ClientList

